using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM;

namespace YseStore.IService.Email.SendCloud
{
    public interface ISendCloudServices
    {


        /// <summary>
        /// 发送sendcloud邮件
        /// </summary>
        /// <param name="toEmail"></param>
        /// <param name="Msub<PERSON>"></param>
        /// <param name="M<PERSON>"></param>
        /// <param name="type"></param>
        /// <param name="fromData"></param>
        /// <returns></returns>
        Task<WebApiCallBack> sendSendcloud(string emailConf, List<string> toEmail, string Msubject, string Mbody, int type = 0, Dictionary<string, string> fromData = null);

    }
}
