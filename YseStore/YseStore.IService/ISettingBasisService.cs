using Entitys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM.Setting;

namespace YseStore.IService
{
    public interface ISettingBasisService
    {
        Task<IList<config>> GetConfigsAry();
        /// <summary>
        /// 更改基本设置
        /// </summary>
        /// <param name="configList"></param>
        /// <returns></returns>
        Task<bool> UpdetConfigAry(List<config> configList);
        /// <summary>
        /// 更改货币信息
        /// </summary>
        /// <param name="currencyList"></param>
        /// <returns></returns>
        Task<bool> UpdetCurrencyAry(List<currency> currencyList,int ManageDefaultCurrency,int DefaultCurrency);

        /// <summary>
        /// 获取基本信息-货币
        /// </summary>
        /// <returns></returns>
        Task<IList<currency>> GetCurrencyAry();
        /// <summary>
        /// 获取基本信息-货币-后台货币
        /// </summary>
        /// <returns></returns>
        Task<currency> GetCurrencyManageDefaultAry();
        /// <summary>
        /// 获取新增订单第一步的基本信息-货币
        /// </summary>
        /// <returns></returns>
        Task<string> GetOrderCurrencyAry();
        Task<FilesStorageOptions> GetFilesStorageOptions();
    }
}
