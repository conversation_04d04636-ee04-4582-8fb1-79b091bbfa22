using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.VM;
using YseStore.Model;

namespace YseStore.IService.Order
{
    public interface IWayBillListService
    {
        /// <summary>
        /// 查询运单列表
        /// </summary>
        /// <param name="keywords"></param>
        /// <param name="OrderStatus"></param>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PagedList<WayBillListResponse>> QueryAsync(string keywords = "", string OrderStatus = "", int pageNum = 1, int pageSize = 50);

        /// <summary>
        /// 获取物流商string
        /// </summary>
        /// <returns></returns>
        Task<List<CarrierResultResponse>> GetShippingCarrierData();

        /// <summary>
        /// 运单里得一个包裹发货
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="TrackingNumber"></param>
        /// <param name="Carrier"></param>
        /// <param name="CarrierKey"></param>
        /// <param name="CarrierKeyType"></param>
        /// <param name="_DoubleOption"></param>
        /// <param name="ShippingTime"></param>
        /// <param name="Remarks"></param>
        /// <param name="OrderId"></param>
        /// <param name="OrderStatus"></param>
        /// <returns></returns>
        Task<bool> WaybillOrdersModStatus(int WId, string TrackingNumber, string Carrier, string CarrierKey, string CarrierKeyType
                    , string _DoubleOption, string ShippingTime, string Remarks, int OrderId, string OrderStatus);
        /// <summary>
        /// 合单列表信息
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <returns></returns>
        Task<List<WaybillPackageListResponse>> WaybillPackageList(int WId, int OrderId);

        /// <summary>
        /// 合单操作
        /// </summary>
        /// <param name="selectList"></param>
        /// <param name="OrderId"></param>
        /// <param name="WId"></param>
        /// <returns></returns>
        Task<bool> WaybillConsolidation(List<int> selectList, int OrderId, int WId);

        /// <summary>
        /// 获取拆单列表信息
        /// </summary>
        /// <param name="WId"></param>
        /// <returns></returns>
        Task<List<WaybillOrdersProductsResponse>> WaybillOrdersProductsList(int WId);

        /// <summary>
        /// 拆单
        /// </summary>
        /// <param name="WId"></param>
        /// <param name="OrderId"></param>
        /// <param name="OvId"></param>
        /// <param name="waybillDict"></param>
        /// <returns></returns>
        Task<bool> OrdersWaybillSeparate(int WId, int OrderId, int OvId, SortedDictionary<string, string> waybillDict);
    }
}
