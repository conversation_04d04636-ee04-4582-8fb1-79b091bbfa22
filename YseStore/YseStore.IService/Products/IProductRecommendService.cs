using Entitys;
using YseStore.Model;
using YseStore.Model.RequestModels.Products;
using YseStore.Model.ResponseModels.Products;

namespace YseStore.IService.Products
{
    /// <summary>
    /// 推荐产品服务接口
    /// </summary>
    public interface IProductRecommendService
    {
        /// <summary>
        /// 获取推荐产品列表，支持搜索和分页
        /// </summary>
        /// <param name="queryRequest">查询请求参数</param>
        /// <returns>分页后的推荐产品列表</returns>
        Task<PageModel<products_recommend>> GetRecommendList(ProductRecommendQueryRequest queryRequest);

        /// <summary>
        /// 根据ID获取推荐产品详情
        /// </summary>
        /// <param name="id">推荐产品ID</param>
        /// <returns>推荐产品详情</returns>
        Task<products_recommend> GetRecommendById(int id);

        /// <summary>
        /// 删除推荐产品
        /// </summary>
        /// <param name="ids">推荐产品ID列表</param>
        /// <returns>删除结果</returns>
        Task<bool> DeleteRecommends(List<int> ids);

        /// <summary>
        /// 创建或更新推荐产品
        /// </summary>
        /// <param name="request">推荐产品更新请求</param>
        /// <returns>推荐产品ID</returns>
        Task<int> CreateOrUpdateRecommend(ProductRecommendUpdateRequest request);

        /// <summary>
        /// 获取前端推荐产品数据
        /// </summary>
        /// <param name="request">前端推荐产品查询请求参数</param>
        /// <returns>推荐产品数据列表</returns>
        Task<List<ProductRecommendFrontendResponse>> GetFrontendRecommendProducts(ProductRecommendFrontendRequest request);
    }
}
