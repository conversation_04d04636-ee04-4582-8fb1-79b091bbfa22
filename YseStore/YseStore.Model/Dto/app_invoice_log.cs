using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// PayPal Invoice日志
    /// </summary>
    public class app_invoice_log
    {
        /// <summary>
        /// PayPal Invoice日志
        /// </summary>
        public app_invoice_log()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 LogId { get; set; }

        /// <summary>
        /// 请求数据
        /// </summary>
        public System.String RequestData { get; set; }

        /// <summary>
        /// 请求地址
        /// </summary>
        public System.String RequestUrl { get; set; }

        /// <summary>
        /// 返回数据
        /// </summary>
        public System.String ResponseData { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public System.String Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 POrderId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public System.String OId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32 CreateAt { get; set; }
    }
}