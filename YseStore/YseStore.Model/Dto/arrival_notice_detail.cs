using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class arrival_notice_detail
    {
        /// <summary>
        /// 
        /// </summary>
        public arrival_notice_detail()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 到货通知ID
        /// </summary>
        public System.Int32? AId { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        public System.String Language { get; set; }

        /// <summary>
        /// 通知状态
        /// </summary>
        public System.String Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public System.Int32 CreateAt { get; set; }

        /// <summary>
        /// 通知时间
        /// </summary>
        public System.Int32 NotifyAt { get; set; }
    }
}