using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class article_content
    {
        /// <summary>
        /// 
        /// </summary>
        public article_content()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Content_en { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用移动端描述 0-不启用 1-启用
        /// </summary>
        public System.Boolean UsedMobile { get; set; } = false;

        /// <summary>
        /// 移动端描述内容
        /// </summary>
        public System.String MobileDescription { get; set; } = string.Empty;
    }
}