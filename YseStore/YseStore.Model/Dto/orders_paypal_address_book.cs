using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_paypal_address_book
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_paypal_address_book()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 AId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? OrderId { get; set; }

        /// <summary>
        /// 临时订单ID
        /// </summary>
        public System.Int32 TempOrderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsUse { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Account { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String LastName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String AddressLine1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String AddressLine2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String CountryCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PhoneNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String City { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String State { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Country { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PaymentId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String PayerId { get; set; }

        /// <summary>
        /// 订单ID
        /// </summary>
        public System.String PayOrderID { get; set; }
 
        /// <summary>
        /// 
        /// </summary>
        public System.String DiscountNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String ErrorCode { get; set; }

        /// <summary>
        /// 查询状态次数
        /// </summary>
        public System.Boolean CheckCount { get; set; }
    }
}