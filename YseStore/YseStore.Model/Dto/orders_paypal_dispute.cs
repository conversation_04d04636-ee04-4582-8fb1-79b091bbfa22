using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class orders_paypal_dispute
    {
        /// <summary>
        /// 
        /// </summary>
        public orders_paypal_dispute()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 DId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String DisputeID { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public System.String Number { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 OrderId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public System.String OId { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public System.String Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 CreateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 UpdateTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ClaimTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32 ResolvedTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String TransactionsInfo { get; set; }

        /// <summary>
        /// 争议原因
        /// </summary>
        public System.String Reason { get; set; }

        /// <summary>
        /// 争议状态
        /// </summary>
        public System.String Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Currency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Decimal Amount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Offer { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Lifecycle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Channel { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String EvidenceJson { get; set; }

        /// <summary>
        /// 证据文件
        /// </summary>
        public System.String EvidenceFile { get; set; }
    }
}