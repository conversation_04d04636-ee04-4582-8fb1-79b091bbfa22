using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class photo_tags
    {
        /// <summary>
        /// 
        /// </summary>
        public photo_tags()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Name { get; set; }
    }
}