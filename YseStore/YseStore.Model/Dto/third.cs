using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class third
    {
        /// <summary>
        /// 
        /// </summary>
        public third()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int16 TId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Title { get; set; }

        /// <summary>
        /// 代码内容
        /// </summary>
        public System.String Code { get; set; }

        /// <summary>
        /// 终端类型
        /// 0：PC+手机 ，1:PC 2:手机
        /// </summary>
        public System.Int16? CodeType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsUsed { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsMeta { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsBody { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public System.String Source { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? AccTime { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public System.Int16? MyOrder { get; set; }

        /// <summary>
        /// 触发页面
        /// </summary>
        public System.String Trigger { get; set; }

        /// <summary>
        /// 显示页面
        /// </summary>
        public System.String Page { get; set; }

        /// <summary>
        /// 是否提供谷歌测速
        /// </summary>
        public System.Boolean IsGaInsights { get; set; }
    }
}