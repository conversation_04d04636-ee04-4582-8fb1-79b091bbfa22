using SqlSugar;
using System.ComponentModel.DataAnnotations;
using YseStore.Model;

namespace YseStore.Model.Entities
{
    /// <summary>
    /// 代码文件实体类
    /// </summary>
    [SugarTable("code_file")]
    public class CodeFile : RootEntityTkey<long>
    {
        /// <summary>
        /// 文件路径（相对于YseStore/Views目录）
        /// </summary>
        [SugarColumn(Length = 500)]
        [Required]
        public string FilePath { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [SugarColumn(Length = 255)]
        [Required]
        public string FileName { get; set; }

        /// <summary>
        /// 文件内容
        /// </summary>
        [SugarColumn(ColumnDataType = "text")]
        public string Content { get; set; }

        /// <summary>
        /// 文件类型（例如：.liquid, .cshtml等）
        /// </summary>
        [SugarColumn(Length = 50)]
        public string FileType { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// 最后修改用户
        /// </summary>
        [SugarColumn(Length = 100)]
        public string LastModifiedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [SugarColumn(Length = 100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 是否是目录
        /// </summary>
        public bool IsDirectory { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }
    }
} 