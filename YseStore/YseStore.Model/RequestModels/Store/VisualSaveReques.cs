using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.RequestModels.Store
{
    public class SaveRequestModel
    {
        public int? EditPId { get; set; }
        public int? DraftsId { get; set; }
        public int? PagesId { get; set; }
        public string Page { get; set; }
        public long? AssociationId { get; set; }
        public List<int> PId { get; set; } = new List<int>();

        [FromForm(Name = "visual")]
        public Dictionary<string, Dictionary<string, object>> VisualData { get; set; }
    }
}
