using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model;

public class CountryInfoResponse
{
    public string name { get; set; }     // 国家名称（需从country表关联）
    public string code { get; set; }     // 国家代码（Acronym，需从country表关联）
    public decimal tax { get; set; }     // 国家税率（需从country表关联）
    public decimal threshold { get; set; } // 税率起征点（需从country表关联）
    public int phone_code { get; set; }  // 电话区号（需从country表关联）
    public int address_format { get; set; } // 地址格式（需从country表关联）
    public List<string> labels { get; set; } = new List<string>();
    public Dictionary<string, ProvinceInfo> provinces { get; set; } = new Dictionary<string, ProvinceInfo>();
}
public class ProvinceInfo
{
    public string code { get; set; }     // 对应AcronymCode
    public string name { get; set; }     // 对应States
    public decimal tax { get; set; }
    public int SId { get; set; }
}