using Entitys;
using System.Collections.Generic;

namespace YseStore.Model.Response.Products
{
    /// <summary>
    /// 批量价格修改的响应模型
    /// </summary>
    public class BatchPriceResponse
    {
        /// <summary>
        /// 产品变体列表
        /// </summary>
        public List<BatchPriceVariantItem> VariantItems { get; set; } = new List<BatchPriceVariantItem>();
    }

    /// <summary>
    /// 批量价格变体项
    /// </summary>
    public class BatchPriceVariantItem
    {
        /// <summary>
        /// 变体ID
        /// </summary>
        public int CId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 图片路径
        /// </summary>
        public string PicPath { get; set; }

        /// <summary>
        /// SKU
        /// </summary>
        public string SKU { get; set; }

        /// <summary>
        /// 当前价格
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 原价
        /// </summary>
        public decimal OldPrice { get; set; }

        /// <summary>
        /// 属性名称（例如：颜色/尺寸/材质等）
        /// </summary>
        public string AttrName { get; set; }
    }
} 