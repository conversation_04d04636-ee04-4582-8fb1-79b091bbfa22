using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Enums;

namespace YseStore.Model.VM.Sales
{

    public class CartProductSalesResult
    {
        /// <summary>
        /// 产品价格
        /// </summary>
        public decimal Price { get; set; }
        /// <summary>
        /// 产品折扣
        /// </summary>
        public decimal Discount { get; set; }
        /// <summary>
        /// 促销状态
        /// </summary>
        public FlashSaleResult FlashSale { get; set; }
        public string PriceType { get; set; } // "wholesale"批发价, "promotion"限时促销 或空
    }
    public class FlashSaleResult
    {
        /// <summary>
        /// 促销活动ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 是否有促销1有 0没有
        /// </summary>
        public int IsUsed { get; set; }
        /// <summary>
        /// 促销后的价格(最终价格)
        /// </summary>
        public decimal Price { get; set; }
        public decimal OldPrice { get; set; }
        /// <summary>
        /// 减掉的价格
        /// </summary>
        public decimal SalePrice { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal SaleDiscount { get; set; }
        public decimal Discount { get; set; }
        public string Scope { get; set; }
        public Dictionary<string, decimal> CombinationData { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public OfferTypeEnum OfferType { get; set; }
        public bool LimitType { get; set; }
        public Dictionary<string, object> LimitCondition { get; set; }
    }

    public class CalcDiscountResult
    {
        public string DiscountType { get; set; } // 折扣类型
        public string DiscountText { get; set; } // 折扣标题
        public decimal DiscountPrice { get; set; } // 折扣价格(减掉的金额)
        public decimal DiscountPriceShow { get; set; } // 折扣显示价格
        public decimal Discount { get; set; } // 折扣比例
        //public Dictionary<int, object> Gifts { get; set; } // 滿赠礼品
        public Dictionary<int, List<GiftInfo>> Gifts { get; set; }// 滿赠礼品
        public List<int> FId { get; set; } // 滿减活动ID
        public Dictionary<int, decimal> FIdPrice { get; set; } // 每个滿减活动分别优惠的价格
    }
    public class GiftInfo
    {
        public int GiftId { get; set; }
        public int Count { get; set; }
    }
    //public class DiscountRuleProduct
    //{
    //    public int ProId { get; set; }
    //    public int Qty { get; set; }
    //}
    public class DiscountResult
    {
        public decimal DiscountAmount { get; set; }
        public List<GiftInfo> Gifts { get; set; } = new List<GiftInfo>();
    }
}
