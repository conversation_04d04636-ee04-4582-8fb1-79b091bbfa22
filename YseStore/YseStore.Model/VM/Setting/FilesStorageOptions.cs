using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Model.VM.Setting;

/// <summary>
///     存储配置转换对象
/// </summary>
public class FilesStorageOptions
{
    /// <summary>
    ///     存储方式（'LocalStorage','AliYunOSS','QCloudOSS'）
    /// </summary>
    public string StorageType { get; set; } = "LocalStorage";
    /// <summary>
    ///     存储目录
    /// </summary>
    public string Path { get; set; } = "u_file";
  
    /// <summary>
    ///     授权账户
    /// </summary>
    public string? AccessKeyId { get; set; }

    /// <summary>
    ///     授权密钥
    /// </summary>
    public string? AccessKeySecret { get; set; }

    /// <summary>
    ///     节点
    /// </summary>
    public string? Endpoint { get; set; }

    /// <summary>
    ///     桶名称
    /// </summary>
    public string? BucketName { get; set; }

    /// <summary>
    ///     桶绑定域名
    /// </summary>
    public string? BucketBindUrl { get; set; }

    /// <summary>
    ///     文件类型
    /// </summary>
    public string? FileTypes { get; set; } = "gif,jpg,jpeg,png,bmp,xls,xlsx,doc,pdf,mp4,WebM,Ogv,txt,pdf,csv,docx,mp3";

    /// <summary>
    ///     最大允许上传单个文件大小（M）
    /// </summary>
    public int MaxSize { get; set; } = 20;
}
