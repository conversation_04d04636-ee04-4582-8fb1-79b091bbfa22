using Microsoft.Extensions.Logging;
using SqlSugar;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.IService.Blog;
using YseStore.Model.Entities.Blog;

namespace YseStore.Service.Blog
{
    /// <summary>
    /// 博客分类SEO服务实现
    /// </summary>
    public class BlogCategorySeoService : IBlogCategorySeoService
    {
        /// <summary>
        /// 博客分类PageUrl到分类ID的映射缓存Key
        /// </summary>
        public const string KEY_BlogCategoryPageUrlToId = "BlogCategoryPageUrlToId";

        private readonly ILogger<BlogCategorySeoService> _logger;
        private readonly ISqlSugarClient _db;
        private readonly ICaching _caching;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="db">数据库客户端</param>
        /// <param name="caching">缓存服务</param>
        public BlogCategorySeoService(ILogger<BlogCategorySeoService> logger, ISqlSugarClient db, ICaching caching)
        {
            _logger = logger;
            _db = db;
            _caching = caching;
        }

        /// <summary>
        /// 检查博客分类PageUrl是否存在
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <param name="currentCategoryId">当前分类ID（更新时使用，新增时为0）</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsCategoryPageUrlExistAsync(string pageUrl, short currentCategoryId = 0)
        {
            if (string.IsNullOrEmpty(pageUrl))
            {
                return false;
            }

            try
            {
                // 先检查缓存中是否存在该PageUrl对应的分类ID
                var cachedCategoryId = _caching.HashGet<short>(KEY_BlogCategoryPageUrlToId, pageUrl);

                if (cachedCategoryId > 0)
                {
                    // 缓存中存在，检查是否是当前分类的PageUrl
                    return cachedCategoryId != currentCategoryId;
                }

                // 缓存中不存在，查询数据库
                var existingCategory = await _db.Queryable<BlogNewCategory>()
                    .Where(c => c.PageUrl == pageUrl)
                    .FirstAsync();

                if (existingCategory != null)
                {
                    // 将查询结果添加到缓存
                    _caching.HashSet(KEY_BlogCategoryPageUrlToId, pageUrl, existingCategory.CateId);

                    // 如果是当前分类的PageUrl，则不算重复
                    return existingCategory.CateId != currentCategoryId;
                }
                else
                {
                    // PageUrl不存在，也可以缓存这个结果（使用特殊值-1表示不存在）
                    _caching.HashSet(KEY_BlogCategoryPageUrlToId, pageUrl, (short)-1);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查博客分类PageUrl是否存在时出错: {pageUrl}");
                return false;
            }
        }

        /// <summary>
        /// 根据PageUrl获取博客分类ID
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <returns>分类ID</returns>
        public async Task<short> GetCategoryIdByPageUrlAsync(string pageUrl)
        {
            if (string.IsNullOrEmpty(pageUrl))
            {
                return 0;
            }

            try
            {
                // 先检查缓存中是否存在该PageUrl对应的分类ID
                var cachedCategoryId = _caching.HashGet<short>(KEY_BlogCategoryPageUrlToId, pageUrl);

                if (cachedCategoryId != 0)
                {
                    // 缓存中存在，返回结果（-1表示不存在，返回0）
                    return cachedCategoryId > 0 ? cachedCategoryId : (short)0;
                }

                // 缓存中不存在，查询数据库
                var category = await _db.Queryable<BlogNewCategory>()
                    .Where(c => c.PageUrl == pageUrl)
                    .Select(c => c.CateId)
                    .FirstAsync();

                if (category > 0)
                {
                    // 将查询结果添加到缓存
                    _caching.HashSet(KEY_BlogCategoryPageUrlToId, pageUrl, category);
                    return category;
                }
                else
                {
                    // PageUrl不存在，缓存这个结果（使用特殊值-1表示不存在）
                    _caching.HashSet(KEY_BlogCategoryPageUrlToId, pageUrl, (short)-1);
                    return 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"根据PageUrl获取博客分类ID时出错: {pageUrl}");
                return 0;
            }
        }

        /// <summary>
        /// 生成唯一的博客分类PageUrl
        /// </summary>
        /// <param name="baseUrl">基础URL</param>
        /// <param name="currentCategoryId">当前分类ID（更新时使用，新增时为0）</param>
        /// <returns>唯一的PageUrl</returns>
        public async Task<string> GenerateUniqueCategoryPageUrlAsync(string baseUrl, short currentCategoryId = 0)
        {
            if (string.IsNullOrEmpty(baseUrl))
            {
                return string.Empty;
            }

            string cleanUrl = baseUrl;
            string uniqueUrl = StringHelper.FilterURLSpecialSymbol(cleanUrl);
            int counter = 0;

            // 检查URL是否已存在
            while (await IsCategoryPageUrlExistAsync(uniqueUrl, currentCategoryId))
            {
                counter++;
                uniqueUrl = $"{cleanUrl}-{counter}";
            }

            return uniqueUrl;
        }

        /// <summary>
        /// 更新博客分类PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        /// <param name="categoryId">分类ID</param>
        public void UpdateCategoryPageUrlCache(string pageUrl, short categoryId)
        {
            if (string.IsNullOrEmpty(pageUrl) || categoryId <= 0)
            {
                return;
            }

            try
            {
                _caching.HashSet(KEY_BlogCategoryPageUrlToId, pageUrl, categoryId);
                _logger.LogDebug($"更新博客分类PageUrl缓存: {pageUrl} -> {categoryId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新博客分类PageUrl缓存失败: {pageUrl}");
            }
        }

        /// <summary>
        /// 移除博客分类PageUrl缓存
        /// </summary>
        /// <param name="pageUrl">分类PageUrl</param>
        public void RemoveCategoryPageUrlCache(string pageUrl)
        {
            if (string.IsNullOrEmpty(pageUrl))
            {
                return;
            }

            try
            {
                _caching.HashDel(KEY_BlogCategoryPageUrlToId, new List<string> { pageUrl });
                _logger.LogDebug($"移除博客分类PageUrl缓存: {pageUrl}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"移除博客分类PageUrl缓存失败: {pageUrl}");
            }
        }

        /// <summary>
        /// 清除所有博客分类PageUrl缓存
        /// </summary>
        public void ClearAllCategoryPageUrlCache()
        {
            try
            {
                // 获取Hash中的所有键，然后批量删除
                var allHashKeys = _caching.HashGet(KEY_BlogCategoryPageUrlToId);
                if (allHashKeys != null && allHashKeys.Any())
                {
                    var hashKeysToDelete = allHashKeys.Keys.ToList();
                    _caching.HashDel(KEY_BlogCategoryPageUrlToId, hashKeysToDelete);
                }
                _logger.LogInformation("清除所有博客分类PageUrl缓存完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清除所有博客分类PageUrl缓存失败");
            }
        }
    }
}