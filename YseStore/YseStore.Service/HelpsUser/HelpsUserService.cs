using Aop.Api.Domain;
using Entitys;
using Microsoft.AspNetCore.Http.HttpResults;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Service.Order;

namespace YseStore.Service.HelpsUser
{
    public class HelpsUserService : IHelpsUserService
    {
        private readonly ISqlSugarClient db;
        private readonly ICurrencyService _currencyService;
        public HelpsUserService(ISqlSugarClient db)
        {
            this.db = db;
        }
        /// <summary>
        /// 查询用户标签
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="type"></param>
        /// <param name="label"></param>
        /// <returns></returns>
        public int CountCustomerLabel(int userId, string type, string label)
        {
            var count = db.Queryable<user_label_collection>()
                .Where(x=>x.UserId==userId&&x.Type==type&&x.Value==label)
                .ToList().Count;

            return count;
        }

        /// <summary>
        /// 会员权益
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public bool GetUserFreeShipping(int userId)
        {
            if (userId == 0) return false;

            var user = db.Queryable<user>()
                .Where(u => u.UserId == userId)
                .First();

            if (user?.IsFreeShipping == true) return true;

            // 检查会员等级插件
            var isPluginUsed = db.Queryable<plugins>()
                .Where(p => p.ClassName == "user_level" && p.IsInstall == true)
                .Any();

            if (userId > 0 && isPluginUsed)
            {
                var level = db.Queryable<user_level>()
                    .Where(l => l.LId == user.Level)
                    .First();

                if (level?.IsFreeShipping == true) return true;
            }

            return false;
        }


        /// <summary>
        /// 成功下单获取积分
        /// </summary>
        /// <param name="getTime">获得积分的时间</param>
        /// <param name="ordersRow">订单数据</param>
        public void PointsPlaceorder(string getTime, orders ordersRow, decimal amount=0m)
        {
            var _config = db.Queryable<config>()
                .Where(x => x.GroupId == "member_points" && x.Variable == "config").First();
            dynamic obj = JObject.Parse(_config.Value);
            // 模拟配置加载（实际应从数据库/配置文件读取）
            var mConfig = new
            {
                order = new
                {
                    isUsed = obj.order.isUsed.Value,
                    orderTime = obj.order.orderTime.Value,
                    amount = obj.order.amount.Value,
                    points = obj.order.points.Value
                }
            };

            // 计算积分
            int points = Convert.ToInt32(Math.Floor(amount / Convert.ToDecimal(mConfig.order.amount)) * Convert.ToDecimal(mConfig.order.points));

            // 检查是否已存在积分记录
            bool hasLog = db.Queryable<member_points_log>()
                .Where(log =>
                    log.SourceType == "placeorder" &&
                    log.UserId == ordersRow.UserId &&
                    log.OrderId == ordersRow.OrderId)
                .Any();

            if (points > 0 && !hasLog)
            {
                // 插入积分日志
                var log = new member_points_log 
                {
                    UserId = Convert.ToInt32(ordersRow.UserId),
                    OrderId = ordersRow.OrderId,
                    TempOrderId = ordersRow.TempOrderId,
                    Type= "income",
                    Points = points,
                    RemainPoints= points,
                    SourceType = "placeorder",
                    SourceInfo="",
                    CreatedAt=DateTimeHelper.ConvertToUnixTimestamp(DateTime.Now)
                };

                db.Insertable(log).ExecuteCommand();
            }
        }


        /// <summary>
        /// 更新优惠券过期状态
        /// </summary>
        /// <param name="couponRow">优惠券记录</param>
        /// <param name="orderId">订单ID</param>
        /// <param name="tempOrderId">临时订单ID</param>
        public void UpdateCouponStatus(dynamic couponRow, int orderId = 0, int tempOrderId = 0)
        {
            DateTime currentTime = DateTime.Now;
            int couponId = couponRow?.CId ?? 0;
            string email = "";
            int userId = 0;

            // 获取用户信息
            if (orderId != 0)
            {
                var order = db.Queryable<orders>()
                    .Where(x => x.OrderId == orderId)
                    .First();
                if (order != null)
                {
                    userId =Convert.ToInt32(order.UserId);
                    email = order.Email;
                }
            }
            else if (tempOrderId != 0)
            {
                var tempOrder = db.Queryable<temp_orders>().InSingle(tempOrderId);
                if (tempOrder != null)
                {
                    email = tempOrder.Email;
                }
            }

            // 构建查询条件
            //var where = PredicateBuilder.True<SalesCouponRelation>();
            //if (userId != 0)
            //{
            //    where = where.And(c => c.CId == couponId && c.UserId == userId);
            //}
            //else if (!string.IsNullOrEmpty(email))
            //{
            //    where = where.And(c => c.CId == couponId && c.Email == email);
            //}

            if (userId != 0|| !string.IsNullOrEmpty(email))
            {
                var couponRelation = db.Queryable<sales_coupon_relation>()
                    .WhereIF(userId != 0, c => c.CId == couponId && c.UserId == userId)
                    .WhereIF(!string.IsNullOrEmpty(email), c => c.CId == couponId && c.Email == email)
                                     .First();

                if (couponRelation != null)
                {
                    int status = 1;
                    DateTime start = DateTimeHelper.ConvertToBeijingTime(couponRelation.StartTime);
                    DateTime end = DateTimeHelper.ConvertToBeijingTime(couponRelation.EndTime);

                    // 检查时间有效性
                    if (currentTime < start) status = -1;    // 未开始
                    else if (currentTime > end) status = -2;  // 已过期
                    else
                    {
                        // 检查使用次数
                        int usedCount = db.Queryable<sales_coupon_log>()
                                        .Where(l => l.CId == couponId &&
                                                   l.Email == email &&
                                                   l.Status == "used")
                                        .Count();

                        if (couponRelation.UnLmUseNum == false &&
                            usedCount >= couponRelation.UseNum)
                        {
                            status = -3; // 次数用完
                        }
                    }

                    // 更新过期状态
                    db.Updateable<sales_coupon_relation>()
                      .SetColumns(c => new sales_coupon_relation
                      {
                          IsExpired = (SByte)(status < 0 ? 1 : 0)
                      })
                       .WhereIF(userId != 0, c => c.CId == couponId && c.UserId == userId)
                    .WhereIF(!string.IsNullOrEmpty(email), c => c.CId == couponId && c.Email == email)
                      .ExecuteCommand();
                }
            }
        }
















        }
}
