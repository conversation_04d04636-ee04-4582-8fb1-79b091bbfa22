using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using SixLabors.ImageSharp;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Microsoft.AspNetCore.Http;
using YseStore.IService;
using YseStore.Model.VM.Setting;
using Flurl.Http;
using YseStore.Model.VM.Aliyun;
using Entitys;

namespace YseStore.Service
{

    public class SettingPhotosService : BaseServices<manage_operation_log>, ISettingPhotosService
    {
        private readonly ILogger<manage_operation_log> _logger;
        private readonly IConfiguration _configuration;
        private readonly ISqlSugarClient db;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISettingBasisService _settingBasisService;

        public SettingPhotosService(ILogger<manage_operation_log> logger
            , IConfiguration configuration
            , ISqlSugarClient db
            , IHttpContextAccessor httpContextAccessor
            , ISettingBasisService settingBasisService
            )
        {
            _logger = logger;
            _configuration = configuration;
            this.db = db;
            _httpContextAccessor = httpContextAccessor;
            _settingBasisService = settingBasisService;
        }

        public async Task<string> GetWebSiteUrl()
        {
            var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
            var WebSiteUrl = string.Empty;
            switch (filesStorageOptions.StorageType)
            {
                case Consts.FilesStorageType_AliYunOSS:
                    WebSiteUrl = filesStorageOptions.BucketBindUrl;
                    break;
                default:
                    WebSiteUrl = $"{_httpContextAccessor.HttpContext!.Request.Scheme}://{_httpContextAccessor.HttpContext.Request.Host.Value}";
                    break;
            }
            return WebSiteUrl;
        }


        /// <summary>
        /// 根据条件查询文件管理
        /// </summary>
        /// <param name="pageNum"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedList<PhotoResponse>> QueryAsync(
            string keyword = "",
            string TagsId = "",
            string id = "",
            int pageNum = 1,
            int pageSize = 50)
        {


            try
            {
                RefAsync<int> totalNum = 0;
                var query = db.Queryable<photo>();
                //if (!string.IsNullOrEmpty(id)&&id== "ordersPicDetail")
                //{
                //    query = query.Where(p => p.IsSystem == "products");
                //}
                //.LeftJoin<photo_tags>((p, pt) => p.TagsId.Contains(pt.Id.ToString()));

                // 搜索条件：标题包含关键词
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(p => p.Name.Contains(keyword));
                }
                // 标签过滤
                if (!string.IsNullOrEmpty(TagsId))
                {
                    // 将传入的 tagId 按逗号分隔为单个标签 ID 列表
                    var tagIdList = TagsId.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(t => t.Trim())
                        .Where(t => !string.IsNullOrEmpty(t))
                        .ToList();

                    query = query.Where(p => p.TagsId != null && tagIdList.All(tag => p.TagsId.Contains(tag)));
                }
                var photoList = query
                    .OrderBy(p => p.PId, OrderByType.Desc)
                    .OrderBy(p=>p.Name,OrderByType.Asc)
                    .ToList();
                var paged = photoList.Skip((pageNum - 1) * pageSize).Take(pageSize).ToList();
                var grouped = paged
                  .Select(o => (PhotoResponse)o)
                  .OrderByDescending(o => o.PId)
                  .ToList();
                var TagsIds = grouped.Where(x => x.TagsId != "").Select(p => p.TagsId).ToList();
              


                var allTagIds = grouped.Where(x => x.TagsId != "")
                    .SelectMany(p => p.TagsId.Split(','))  // 拆分逗号分隔的字符串
                    .Select(int.Parse)                     // 转为整数（假设 Id 是 int 类型）
                    .Distinct()                            // 去重
                    .ToList();
                var TagsNames = db.Queryable<photo_tags>()
                    .Where(pt => allTagIds.Contains(pt.Id)) // 精确匹配数字 ID
                    .ToList();


                foreach (var p in grouped)
                {
                    p.Suffix = GetSuffix(p.PicPath);
                    if (!string.IsNullOrEmpty(p.TagsId))
                    {
                        var tagIds = p.TagsId.Split(',')
                                        .Select(int.Parse) // 转为整数列表
                                        .ToList();

                        // 用精确匹配代替模糊匹配
                        var Names = TagsNames.Where(t => tagIds.Contains(t.Id)) // 假设 t.Id 是 int 类型
                                            .Select(t => t.Name)
                                            .ToList();

                        p.TagsName = string.Join(",", Names);
                    }


                }









                //var res = query.Select((p, pt) => new
                //{
                //    Photo = p,
                //    TagName = pt.Name
                //})
                // .ToList();

                //var grouped = res.GroupBy(item => item.Photo.PId)
                //    .Select(g => new PhotoResponse()
                //    {
                //        PId = g.Key,
                //        Name = g.First().Photo.Name,
                //        PicPath = g.First().Photo.PicPath,
                //        Width = g.First().Photo.Width,
                //        Height = g.First().Photo.Height,
                //        Suffix = GetSuffix(g.First().Photo.PicPath),
                //        IsSystem = g.First().Photo.IsSystem,
                //        TagsName = string.Join(",", g.Select(x => x.TagName).Distinct()),
                //        WebSiteUrl = g.First().Photo.Host,
                //    })
                //    .OrderByDescending(p => p.PId)
                //    .ToList();

                // 分页处理
                var pl = new PagedList<PhotoResponse>(grouped, pageNum - 1, pageSize, photoList.Count);
                return pl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询文件管理失败");

            }
            return null;
        }
        /// <summary>
        /// 获取图片标签
        /// </summary>
        /// <returns></returns>
        public async Task<List<photo_tags>> GetPhoto_Tags()
        {
            List<photo_tags> photo_TagsList = new List<photo_tags>();
            photo_TagsList = await db.Queryable<photo_tags>()
           .GroupBy(it => new { it.Name })
           .Select(it => new photo_tags
           {
               Id = SqlFunc.AggregateMax(it.Id),
               Name = it.Name
           })
           .ToListAsync();
            return photo_TagsList;
        }

        public async Task<List<photo_tags>> GetPhoto_Tags_Tags(string idString)
        {
            var idList = idString.Split('-').Select(int.Parse).ToList();
            var photoQuery = db.Queryable<photo>()
                .Where(it => idList.Contains(Convert.ToInt32(it.PId)))
                .ToList();

            var tagIds = new List<int>();
            foreach (var photo in photoQuery)
            {
                if (!string.IsNullOrEmpty(photo.TagsId))
                {
                    var ids = photo.TagsId.Split(',')
                                         .Select(int.Parse)
                                         .ToList();
                    tagIds.AddRange(ids);
                }
            }

            tagIds = tagIds.Distinct().ToList();
            var tagQuery = db.Queryable<photo_tags>()
                .Where(it => tagIds.Contains(it.Id))
                .ToList();
            return tagQuery;
        }
        /// <summary>
        /// 获取图片标签数组
        /// </summary>
        /// <returns></returns>
        public async Task<List<TagDtoResponse>> GetFormattedPhotoTags()
        {
            var query = await db.Queryable<photo_tags>()
                .GroupBy(it => it.Name)
                .Select(it => new TagDtoResponse
                {
                    Name = it.Name,
                    Value = SqlFunc.AggregateMax(it.Id),
                    Type = "photo_tags"
                }).ToListAsync();

            return query;
        }
        public async Task<(bool, Aliyun_OssObj)> GetAliyunImageInfo(string fullUrl)
        {
            var info = new Aliyun_OssObj();
            var status = true;
            try
            {
                info = await $"{fullUrl}?x-oss-process=image/info".GetJsonAsync<Aliyun_OssObj>();
            }
            catch (Exception)
            {
                status = false;


            }
            return (status, info);
        }

        /// <summary>
        /// 新增图片
        /// </summary>
        /// <param name="FilePath"></param>
        /// <param name="tagsName"></param>
        /// <returns></returns>
        public async Task<bool> AddImageList(List<string> FilePath, List<string> fizeSize, List<string> oriName, List<string> tagsName)
        {
            //var result = await db.Ado.UseTranAsync(async () =>
            //{
            var WebSiteUrl = await GetWebSiteUrl();

            int stopIndex = tagsName.FindIndex(tagName => tagName == "截至-·11");
            if (stopIndex != -1)
            {
                tagsName = tagsName.Take(stopIndex).ToList();
            }

            List<int> tagIds = new List<int>();

            if (tagsName != null && tagsName.Count > 0)
            {
                foreach (var tagName in tagsName.Distinct())
                {
                    var existingTag = await db.Queryable<photo_tags>()
                        .Where(t => t.Name == tagName)
                        .Select(t => new { t.Id })
                        .FirstAsync();
                    if (existingTag != null)
                    {
                        // 存在则直接使用现有ID
                        tagIds.Add(existingTag.Id);
                    }
                    else
                    {
                        // 不存在则插入新标签
                        var newTag = new photo_tags { Name = tagName };
                        var newId = await db.Insertable(newTag).ExecuteReturnIdentityAsync();
                        tagIds.Add((int)newId);
                    }
                }
            }
            var len = FilePath.Count;
            for (var i = 0; i < len; i++)
            {
                var path = FilePath[i];
                var imgName = Path.GetFileName(path);
                var file = new photo
                {
                    Name = Path.GetFileNameWithoutExtension(oriName[i]),
                    PicPath = path,
                    Width = 0,// imageInfo.width,
                    Height = 0,// imageInfo.height,
                    IsSystem = "products",
                    TagsId = string.Join(",", tagIds),
                    Host = WebSiteUrl,
                    Size = fizeSize[i].ObjToLong(),
                    OriName = Path.GetFileNameWithoutExtension(oriName[i])


                };
                var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
                switch (filesStorageOptions.StorageType)
                {
                    case Consts.FilesStorageType_AliYunOSS:
                        var imgInfo = await GetAliyunImageInfo(path);
                        if (imgInfo.Item1)
                        {
                            file.Width = imgInfo.Item2.ImageWidth.value.ObjToInt();
                            file.Height = imgInfo.Item2.ImageHeight.value.ObjToInt();
                        }
                        break;
                }
                await db.Insertable(file).ExecuteReturnIdentityAsync();

            }

            return true;
            //});
            //return Convert.ToBoolean(result);

        }

        /// <summary>
        /// 图库中选择后，添加数据库
        /// </summary>
        /// <param name="FilePath"></param>
        /// <param name="oriName"></param>
        /// <returns></returns>
        public async Task<bool> AddImageGallery(string FilePath, string name, string oriname, string type,long size)
        {
            var WebSiteUrl = await GetWebSiteUrl();

           
                var path = FilePath;
                var imgName = Path.GetFileName(path);
                var file = new photo
                {
                    Name = name,
                    PicPath = path,
                    Width = 0,// imageInfo.width,
                    Height = 0,// imageInfo.height,
                    IsSystem = type,
                    TagsId = "",
                    Host = WebSiteUrl,
                    OriName = oriname,
                    Size= size

                };
                var filesStorageOptions = await _settingBasisService.GetFilesStorageOptions();
                switch (filesStorageOptions.StorageType)
                {
                    case Consts.FilesStorageType_AliYunOSS:
                        var imgInfo = await GetAliyunImageInfo(path);
                        if (imgInfo.Item1)
                        {
                            file.Width = imgInfo.Item2.ImageWidth.value.ObjToInt();
                            file.Height = imgInfo.Item2.ImageHeight.value.ObjToInt();
                        }
                        break;
                }
                await db.Insertable(file).ExecuteReturnIdentityAsync();

            

            return true;
            //});
            //return Convert.ToBoolean(result);

        }



        /// <summary>
        /// 通过pId更新图片表中的标签id
        /// </summary>
        /// <param name="pIdString"></param>
        /// <param name="tagsNameList"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<bool> Updatephoto_tagsId(string pIdString, List<string> tagsNameList)
        {
            bool b = true;
            List<int> pIds;
            try
            {
                pIds = pIdString.Split('-').Select(int.Parse).ToList();
            }
            catch (FormatException)
            {
                throw new ArgumentException("PId格式无效，必须为数字");
            }
            int stopIndex = tagsNameList.FindIndex(tagName => tagName == "截至-·11");
            if (stopIndex != -1)
            {
                tagsNameList = tagsNameList.Take(stopIndex).ToList();
            }
            List<int> tagIds = new List<int>();

            if (tagsNameList != null && tagsNameList.Count > 0)
            {
                foreach (var tagName in tagsNameList.Distinct())
                {
                    var existingTag = await db.Queryable<photo_tags>()
                        .Where(t => t.Name == tagName)
                        .Select(t => new { t.Id })
                        .FirstAsync();
                    if (existingTag == null)
                    {
                        var insertObj = new photo_tags();
                        insertObj.Name = tagName;
                        int id = db.Insertable(insertObj).ExecuteReturnIdentity();
                        tagIds.Add(id);
                    }
                    else
                    {
                        tagIds.Add(existingTag.Id);
                    }

                }
            }


            string tagsNameString = string.Join(",", tagIds);

            //string tagsNameString = string.Join(",", tagsNameList);
            var entities = db.Queryable<photo>()
                .Where(it => pIds.Contains(Convert.ToInt32(it.PId)))
                .ToList();

            foreach (var entity in entities)
            {
                entity.TagsId = tagsNameString;
                bool bt = await db.Updateable(entity).ExecuteCommandHasChangeAsync();
                if (!bt)
                {
                    b = false;
                }
            }
            return b;
        }
        /// <summary>
        /// 删除图片
        /// </summary>
        /// <param name="pIdString"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public async Task<bool> Delphoto(string pIdString)
        {
            List<int> pIds;
            try
            {
                pIds = pIdString.Split('-').Select(int.Parse).ToList();
            }
            catch (FormatException)
            {
                throw new ArgumentException("PId格式无效，必须为数字");
            }

            var result = await db.Deleteable<photo>()
                .In(p => p.PId, pIds)
                .ExecuteCommandAsync();
            return result > 0;
        }

        //public static (string fileName, int width, int height) GetImageInfo(string imagePath)
        //{
        //    // 获取不带后缀的文件名
        //    string fileName = Path.GetFileNameWithoutExtension(imagePath);

        //    // 获取文件后缀名
        //    //string extension = Path.GetExtension(imagePath);

        //    // 使用using确保及时释放资源
        //    using (Image image = Image.FromFile(imagePath))
        //    {
        //        // 获取图片宽高
        //        int width = image.Width;
        //        int height = image.Height;

        //        return (fileName, width, height);
        //    }
        //}
        public static (string fileName, int width, int height) GetImageInfo(string imagePath)
        {
            try
            {
                imagePath = "wwwroot" + imagePath;
                string fileName = Path.GetFileNameWithoutExtension(imagePath);

                using (Image image = Image.Load(imagePath))
                {
                    return (fileName, image.Width, image.Height);
                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }
        public static string GetSuffix(string filePath)
        {
            var str = "";
            // 从后往前查找第一个点号的位置
            int dotIndex = filePath.LastIndexOf('.');
            // 检查是否找到点号且不在开头位置
            if (dotIndex > 0 && dotIndex < filePath.Length - 1)
            {
                str = filePath.Substring(dotIndex);
            }
            return str;
        }
        /// <summary>
        /// 根据关键字获取图片标签列表
        /// </summary>
        /// <param name="Keyword"></param>
        /// <param name="Type"></param>
        /// <returns></returns>
        public async Task<List<photo_tags>> getPhotoTagsContains(string Keyword, string Type)
        {
            var ret = db.Queryable<photo_tags>(Keyword)
                .Where(x => x.Name.Contains(Keyword))
                .ToList();
            return ret;
        }





    }
}
