using Entitys;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using System.Security.Claims;
using Wangkanai.Extensions;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.IService.Store;
using Microsoft.AspNetCore.Mvc.Filters;

namespace YseStore.Controllers
{
    public class BaseController : Controller
    {
        protected readonly IMenuService _menuService;

        public BaseController(IMenuService menuService = null)
        {
            _menuService = menuService;
        }

        public ClaimsIdentity CurrentUser
        {
            get
            {
                var user = (ClaimsIdentity)User.Identity;
                if (user != null && user.Claims.ToList().Count > 0)
                {
                    return user;
                }
                else
                {

                    return null;
                }

            }
        }

        public int CurrentUserId
        {
            get
            {
                if (User.Identity.IsAuthenticated)
                {
                    var s = int.TryParse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value, out int userid);
                    if (s)
                    {
                        return userid;
                    }
                }
                return 0; // 未登录用户返回默认值
            }
        }


        public string CurrentUserName
        {
            get
            {
                if (User.Identity.IsAuthenticated)
                {
                    return User.FindFirst(ClaimTypes.Name)?.Value ?? "Guest";
                }
                return "Guest"; // 未登录用户返回默认值
            }
        }

        public string CurrentUserEmail
        {
            get
            {
                if (User.Identity.IsAuthenticated)
                {
                    return User.FindFirst(ClaimTypes.Email)?.Value ?? "";
                }
                return ""; // 未登录用户返回默认值
            }
        }

        /// <summary>
        /// 获取当前用户的SessionId
        /// </summary>
        public string CurrentUserSessionId
        {
            get
            {
                string sessionId = HttpContext.GetCookies("sessionId");
                if (!sessionId.IsNullOrEmpty())
                {
                    // 如果用户已登录，返回当前用户的SessionId
                    return sessionId;
                }

                sessionId = HttpContext.Session.Id;
                HttpContext.SetCookies("sessionId", sessionId, 60 * 24 * 30);//一个月有效期
                return sessionId; // 未登录用户返回默认值

            }
        }




        /// <summary>
        /// 获取当前语言设置
        /// </summary>
        public string CurrentLang
        {
            get
            {
                string lang = HttpContext.GetCookies("lang");
                if (!lang.IsNullOrEmpty())
                {
                    return lang;
                }
                else
                {
                    if (HttpContext.Session.TryGetValue("lang", out byte[] currencyBytes) && currencyBytes.Length > 0)
                    {
                        lang = System.Text.Encoding.UTF8.GetString(currencyBytes);
                        HttpContext.SetCookies("lang", lang);
                        return lang;
                    }
                    else
                    {
                        //如果没有设置语言，则使用默认语言
                        HttpContext.SetCookies("lang", AppSettingsConstVars.LanguageDefault);
                        return AppSettingsConstVars.LanguageDefault;
                    }
                }
            }
            set
            {
                if (!value.IsNullOrEmpty())
                {
                    //一个月有效期
                    HttpContext.SetCookies("lang", value, 60 * 24 * 30);

                    string culture = "";
                    if (GlobalConstVars.lang_culture.ContainsKey(value))
                    {
                        culture = GlobalConstVars.lang_culture[value];

                        //设置
                        CookieOptions cookieOptions = new CookieOptions();
                        cookieOptions.Expires = DateTimeOffset.UtcNow.AddMonths(1);
                        cookieOptions.IsEssential = true;
                        HttpContext.Response.Cookies.Append(
                            CookieRequestCultureProvider.DefaultCookieName,
                            CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                            cookieOptions
                        );
                    }
                }
            }

            //get
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {
            //        return User.FindFirst("lang")?.Value ?? AppSettingsConstVars.LanguageDefault;
            //    }
            //    else
            //    {
            //        //获取Session中的语言设置
            //        if (HttpContext.Session.TryGetValue("lang", out byte[] langBytes) && langBytes.Length > 0)
            //        {
            //            return System.Text.Encoding.UTF8.GetString(langBytes);
            //        }
            //        else
            //        {
            //            //如果Session中没有语言设置，则使用默认语言
            //            return AppSettingsConstVars.LanguageDefault;
            //        }
            //    }
            //}
            //set
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {
            //        //var curclaim = CurrentUser.FindFirst("lang");

            //        ////更新token
            //        //await UpdateToken(curclaim, "lang", value);

            //    }
            //    else
            //    {
            //        //设置session
            //        HttpContext.Session.SetString("lang", value);
            //    }

            //}
        }

        //public async Task SetTokenCurrentLang(string lang)
        //{

        //    var curclaim = CurrentUser.FindFirst("lang");

        //    //更新token
        //    await UpdateToken(curclaim, "lang", lang);

        //}


        /// <summary>
        /// 销售分区
        /// </summary>
        public string CurrentSalesAreaCode
        {
            get
            {
                string salesAreaCode = HttpContext.GetCookies("salesAreaCode");
                if (!salesAreaCode.IsNullOrEmpty())
                {
                    return salesAreaCode;
                }
                else
                {
                    //获取Session中的分区设置
                    if (HttpContext.Session.TryGetValue("salesAreaCode", out byte[] currencyBytes) && currencyBytes.Length > 0)
                    {
                        salesAreaCode = System.Text.Encoding.UTF8.GetString(currencyBytes);
                        HttpContext.SetCookies("salesAreaCode", salesAreaCode);
                        return salesAreaCode;
                    }
                    else
                    {
                        //默认货币符号
                        HttpContext.SetCookies("salesAreaCode", AppSettingsConstVars.SalesAreaCodeDefault);
                        return AppSettingsConstVars.SalesAreaCodeDefault;
                    }
                }
            }
            set
            {
                if (!value.IsNullOrEmpty())
                {
                    HttpContext.SetCookies("salesAreaCode", value);
                }
            }
        }

        /// <summary>
        /// 获取当前货币设置
        /// </summary>
        public string CurrentCurrency
        {
            get
            {
                string currency = HttpContext.GetCookies("currency");
                if (!currency.IsNullOrEmpty())
                {
                    return currency;
                }
                else
                {
                    //获取Session中的货币设置
                    if (HttpContext.Session.TryGetValue("currency", out byte[] currencyBytes) && currencyBytes.Length > 0)
                    {
                        currency = System.Text.Encoding.UTF8.GetString(currencyBytes);
                        HttpContext.SetCookies("currency", currency);
                        return currency;
                    }
                    else
                    {
                        HttpContext.SetCookies("currency", AppSettingsConstVars.CurrentDefault);
                        return AppSettingsConstVars.CurrentDefault;
                    }

                }
            }
            set
            {
                if (!value.IsNullOrEmpty())
                {
                    HttpContext.SetCookies("currency", value);
                }
            }


            //get
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {
            //        return User.FindFirst("currency")?.Value ?? AppSettingsConstVars.CurrentDefault;
            //    }
            //    else
            //    {
            //        //获取Session中的货币设置
            //        if (HttpContext.Session.TryGetValue("currency", out byte[] currencyBytes) && currencyBytes.Length > 0)
            //        {
            //            return System.Text.Encoding.UTF8.GetString(currencyBytes);
            //        }
            //        else
            //        {
            //            //如果Session中没有货币设置，则使用默认货币
            //            return AppSettingsConstVars.CurrentDefault;
            //        }
            //    }
            //}
            //set
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {


            //    }
            //    else
            //    {
            //        //设置session
            //        HttpContext.Session.SetString("currency", value);
            //    }

            //}
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currency"></param>
        /// <returns></returns>
        //public async Task SetTokenCurrentCurrency(string currency)
        //{

        //    var curclaim = CurrentUser.FindFirst("currency");

        //    //更新token
        //    await UpdateToken(curclaim, "currency", currency);

        //}

        /// <summary>
        /// 默认币种符号
        /// </summary>
        public string CurrentCurrencySymbol
        {
            get
            {
                string currency = HttpContext.GetCookies("currencySymbol");
                if (!currency.IsNullOrEmpty())
                {
                    return currency;
                }
                else
                {
                    //获取Session中的货币设置
                    if (HttpContext.Session.TryGetValue("currencySymbol", out byte[] currencyBytes) && currencyBytes.Length > 0)
                    {
                        currency = System.Text.Encoding.UTF8.GetString(currencyBytes);
                        HttpContext.SetCookies("currencySymbol", currency);
                        return currency;
                    }
                    else
                    {
                        //默认货币符号
                        HttpContext.SetCookies("currencySymbol", AppSettingsConstVars.CurrentDefaultSymbol);
                        return AppSettingsConstVars.CurrentDefaultSymbol;
                    }

                }

            }
            set
            {
                if (!value.IsNullOrEmpty())
                {
                    HttpContext.SetCookies("currencySymbol", value);
                }
            }

            //get
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {
            //        return User.FindFirst("currencySymbol")?.Value ?? AppSettingsConstVars.CurrentDefaultSymbol;
            //    }
            //    else
            //    {
            //        //获取Session中的货币符号设置
            //        if (HttpContext.Session.TryGetValue("currencySymbol", out byte[] currencySymbolBytes) && currencySymbolBytes.Length > 0)
            //        {
            //            return System.Text.Encoding.UTF8.GetString(currencySymbolBytes);
            //        }
            //        else
            //        {
            //            //如果Session中没有货币符号设置，则使用默认货币符号
            //            return AppSettingsConstVars.CurrentDefaultSymbol;
            //        }
            //    }
            //}
            //set
            //{
            //    if (User.Identity.IsAuthenticated)
            //    {
            //        //var curclaim = CurrentUser.FindFirst("currencySymbol");

            //        ////更新token
            //        //await UpdateToken(curclaim, "currencySymbol", value);

            //    }
            //    else
            //    {
            //        //设置session
            //        HttpContext.Session.SetString("currencySymbol", value);
            //    }

            //}
        }

        /// <summary>
        /// 更新token
        /// </summary>
        /// <param name="currencySymbol"></param>
        /// <returns></returns>
        //public async Task SetTokenCurrentCurrencySymbol(string currencySymbol)
        //{

        //    var curclaim = CurrentUser.FindFirst("currencySymbol");

        //    //更新token
        //    await UpdateToken(curclaim, "currencySymbol", currencySymbol);

        //}



        /// <summary>
        /// 获取当前用户的购物车数量
        /// </summary>
        public int CurrentCartCount
        {
            get
            {
                if (User.Identity.IsAuthenticated)
                {
                    string cartCount = User.FindFirst("cartCount")?.Value;
                    if (int.TryParse(cartCount, out int count))
                    {
                        return count;
                    }

                    return 0;
                }
                else
                {
                    // 如果未登录用户，尝试从cookie中获取购物车数量
                    var cartCount = HttpContext.GetCookies("cartCount");
                    if (!string.IsNullOrEmpty(cartCount) && int.TryParse(cartCount, out int count))
                    {
                        return count;
                    }

                    // 如果cookie中没有购物车数量，则返回默认值0
                    return 0;

                    //if (HttpContext.Session.TryGetValue("cartCount", out byte[] cartCountBytes) && cartCountBytes.Length > 0)
                    //{
                    //    string cartCountStr = System.Text.Encoding.UTF8.GetString(cartCountBytes);
                    //    if (int.TryParse(cartCountStr, out int count))
                    //    {
                    //        return count;
                    //    }
                    //}
                    //return 0; // 未登录用户返回默认值

                }
            }
            set
            {
                if (User.Identity.IsAuthenticated)
                {
                    //var curclaim = CurrentUser.FindFirst("cartCount");

                    ////更新token
                    //await UpdateToken(curclaim, "cartCount", value);
                }
                else
                {
                    //设置cookie购物车数量
                    if (value >= 0)
                    {
                        HttpContext.SetCookies("cartCount", value.ToString());
                    }

                }

            }
        }

        public async Task SetTokenCartCount(int cartCount)
        {

            var curclaim = CurrentUser.FindFirst("cartCount");

            //更新token
            await UpdateToken(curclaim, "cartCount", cartCount.ToString());

        }


        /// <summary>
        /// 收藏数量
        /// </summary>
        public int CurrentWishListCount
        {
            get
            {
                if (User.Identity.IsAuthenticated)
                {
                    string cartCount = User.FindFirst("wishListCount")?.Value;
                    if (int.TryParse(cartCount, out int count))
                    {
                        return count;
                    }

                    return 0;
                }
                return 0;
            }
        }

        /// <summary>
        /// 更新token收藏数量 
        /// </summary>
        /// <param name="cartCount"></param>
        /// <returns></returns>
        public async Task SetTokenWishListCount(int cartCount)
        {

            var curclaim = CurrentUser.FindFirst("wishListCount");

            //更新token
            await UpdateToken(curclaim, "wishListCount", cartCount.ToString());

        }


        /// <summary>
        /// 更新token
        /// </summary>
        /// <param name="curclaim"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        public async Task UpdateToken(Claim curclaim, string key, string value)
        {

            var issuer = curclaim.Issuer;
            var oriissuer = curclaim.OriginalIssuer;
            var vType = curclaim.ValueType;

            var s = CurrentUser.TryRemoveClaim(curclaim);
            CurrentUser.AddClaim(new Claim(key, value, vType, issuer, oriissuer));


            var identity = new ClaimsIdentity(CurrentUser.Claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var principal = new ClaimsPrincipal(identity);
            var authOption = new AuthenticationProperties();
            authOption.ExpiresUtc = DateTimeOffset.UtcNow.AddDays(15);

            authOption.IsPersistent = true;
            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal,
                 authOption);


        }





        /// <summary>
        /// 获取导航菜单数据
        /// </summary>
        /// <returns></returns>
        protected async Task<object> GetNavigationMenusAsync()
        {
            try
            {
                if (_menuService == null)
                {
                    return new { NavMenus = new List<object>(), FooterNavMenus = new List<object>() };
                }

                // 获取导航菜单数据
                var navMenus = await _menuService.GetNavMenusAsync(CurrentLang);

                // 获取底部导航菜单数据
                var footerNavMenus = await _menuService.GetFooterNavMenusAsync(CurrentLang);

                return new { NavMenus = navMenus, FooterNavMenus = footerNavMenus };
            }
            catch (Exception ex)
            {
                // 记录异常
                Console.WriteLine($"获取导航菜单数据时出错: {ex.Message}");

                // 返回空模型
                return new { NavMenus = new List<object>(), FooterNavMenus = new List<object>() };
            }
        }

    }
}
