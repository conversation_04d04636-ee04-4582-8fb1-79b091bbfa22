using MessagePack;
using YseStore.Model.VM;

namespace YseStore.Ext
{
    public static class CommonExt
    {       /// <summary>
            /// 
            /// </summary>
            /// <param name="jm"></param>
            /// <returns></returns>
        public static MemoryStream GetMemoryStream(WebApiCallBack jm)
        {

            byte[] byteArray = MessagePackSerializer.Serialize(jm);
            MemoryStream ms = new MemoryStream(byteArray);

            return ms;
        }


        public static MemoryStream GetMemoryStream<T>(WebApiCallBack<T> jm)
        {

            byte[] byteArray = MessagePackSerializer.Serialize(jm);
            MemoryStream ms = new MemoryStream(byteArray);

            return ms;
        }
    }
}
