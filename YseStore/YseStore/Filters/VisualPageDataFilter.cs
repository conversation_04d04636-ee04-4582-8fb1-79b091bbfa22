using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using YseStore.Common.Cache;
using YseStore.IService.Visual;
using YseStore.Common.Helper;
using YseStore.Common.Const;

namespace YseStore.Filters
{
    /// <summary>
    /// 全局可视化页面数据过滤器
    /// 自动为所有页面注入VisualPageData，无需在Controller中手动获取
    /// </summary>
    public class VisualPageDataFilter : IAsyncActionFilter
    {
        private readonly IVisualPageBuilderService _visualPageBuilderService;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPageDataFilter> _logger;

        public VisualPageDataFilter(
            IVisualPageBuilderService visualPageBuilderService,
            ICaching caching,
            ILogger<VisualPageDataFilter> logger)
        {
            _visualPageBuilderService = visualPageBuilderService;
            _caching = caching;
            _logger = logger;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            try
            {
                // 获取当前语言
                var currentLang = GetCurrentLanguage(context.HttpContext);
                
                // 根据Controller和Action确定页面类型
                var pageType = DeterminePageType(context);
                
                // 获取可视化页面数据（带缓存）
                var visualPageData = await GetVisualPageDataAsync(pageType, currentLang);
                var visualPageDataJson = visualPageData?.ToJson() ?? "{}";

                // 将数据注入到ViewData中，所有View都能访问
                context.HttpContext.Items["VisualPageData"] = visualPageDataJson;
                context.HttpContext.Items["VisualPageDataObject"] = visualPageData;
                
                // 继续执行Action
                var executedContext = await next();
                
                // 如果是返回View的结果，自动注入到ViewData
                if (executedContext.Result is ViewResult viewResult)
                {
                    viewResult.ViewData["VisualPageData"] = visualPageDataJson;
                    viewResult.ViewData["VisualPageDataObject"] = visualPageData;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可视化页面数据时出错");
                
                // 设置默认值，确保页面正常显示
                context.HttpContext.Items["VisualPageData"] = "{}";
                context.HttpContext.Items["VisualPageDataObject"] = null;
                
                await next();
            }
        }

        /// <summary>
        /// 获取可视化页面数据（带缓存）
        /// </summary>
        private async Task<object> GetVisualPageDataAsync(string pageType, string currentLang)
        {
            try
            {
                // 使用Hash缓存
                var hashKey = "visual_page_data";
                var fieldKey = $"{pageType}_{currentLang}";

                // 尝试从Hash缓存获取
                var cachedData = _caching.HashGet<object>(hashKey, fieldKey);
                if (cachedData != null)
                {
                    return cachedData;
                }

                // 从服务获取数据
                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync(pageType);

                // 缓存数据到Hash
                if (visualPageData != null)
                {
                    _caching.HashSet(hashKey, fieldKey, visualPageData);
                }

                return visualPageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可视化页面数据失败，PageType: {PageType}", pageType);
                return null;
            }
        }

        /// <summary>
        /// 根据Controller和Action确定页面类型
        /// </summary>
        private string DeterminePageType(ActionExecutingContext context)
        {
            var controllerName = context.RouteData.Values["controller"]?.ToString()?.ToLower();
            var actionName = context.RouteData.Values["action"]?.ToString()?.ToLower();

            return controllerName switch
            {
                "home" => "index",
                "blog" => "blog", 
                "shop" => "shop",
                "product" => "product",
                _ => "default"
            };
        }

        /// <summary>
        /// 获取当前语言
        /// </summary>
        private string GetCurrentLanguage(HttpContext httpContext)
        {
            // 从Cookie获取语言设置
            string lang = httpContext.GetCookies("lang");
            if (!string.IsNullOrEmpty(lang))
            {
                return lang;
            }

            // 从Session获取
            if (httpContext.Session.TryGetValue("lang", out byte[] langBytes) && langBytes.Length > 0)
            {
                return System.Text.Encoding.UTF8.GetString(langBytes);
            }

            // 返回默认语言
            return AppSettingsConstVars.LanguageDefault;
        }
    }
}
