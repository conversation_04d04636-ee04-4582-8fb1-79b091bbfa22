{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5185", "sslPort": 0}}, "profiles": {"T600": {"commandName": "Project", "hotReloadEnabled": false, "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5185", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Theme": "t600", "BodyCss": "home-2", "BodyId": "pageWrapper-2"}}, "Retevis": {"commandName": "Project", "hotReloadEnabled": false, "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5185", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Theme": "t100", "BodyCss": "template-index index-retevis-1 modal-popup-style"}}, "RetekessT200": {"commandName": "Project", "hotReloadEnabled": false, "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://localhost:5185", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Theme": "t200", "BodyCss": "template-index index-demo7"}}, "T300": {"commandName": "Project", "hotReloadEnabled": false, "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5185", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Theme": "t300", "BodyCss": "home-2"}}, "T120": {"commandName": "Project", "hotReloadEnabled": false, "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5185", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Theme": "t120", "BodyCss": "hasScrollbar"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}