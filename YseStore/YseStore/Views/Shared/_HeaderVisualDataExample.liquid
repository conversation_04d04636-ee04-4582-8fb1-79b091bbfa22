{% comment %}
Header中使用自动注入的VisualPageData示例
现在所有页面的Header都能自动获取到可视化配置数据
{% endcomment %}

<!-- 方式1：使用ViewData中的JSON数据 -->
{% assign visualPageData = ViewData.VisualPageData | jsonparse %}

<!-- 方式2：也可以直接使用对象数据 -->
{% assign visualDataObject = ViewData.VisualPageDataObject %}

<!-- 初始化Header配置变量 -->
{% assign showHeader = true %}
{% assign showLogo = true %}
{% assign showSearch = true %}
{% assign showUser = true %}
{% assign showShoppingCart = true %}
{% assign showLanguageSwitch = true %}
{% assign showMenu = true %}
{% assign logoConfig = null %}
{% assign searchPlaceholder = "Search..." %}
{% assign headerStyle = "default" %}
{% assign headerBackgroundColor = null %}
{% assign headerTextColor = null %}

<!-- 解析Header插件配置 -->
{% if visualPageData and visualPageData.PluginsByType and visualPageData.PluginsByType.header %}
    {% assign headerPlugins = visualPageData.PluginsByType.header %}
    {% if headerPlugins.size > 0 %}
        {% assign headerPlugin = headerPlugins[0] %}
        
        <!-- 解析显示配置 -->
        {% if headerPlugin.Config %}
            {% assign displayValue = headerPlugin.Config.Display | append: "" %}
            {% if displayValue == "0" %}
                {% assign showHeader = false %}
            {% endif %}
            
            {% if headerPlugin.Config.Style %}
                {% assign headerStyle = headerPlugin.Config.Style %}
            {% endif %}
            
            {% if headerPlugin.Config.BackgroundColor %}
                {% assign headerBackgroundColor = headerPlugin.Config.BackgroundColor %}
            {% endif %}
            
            {% if headerPlugin.Config.TextColor %}
                {% assign headerTextColor = headerPlugin.Config.TextColor %}
            {% endif %}
        {% endif %}
        
        <!-- 解析功能设置 -->
        {% if headerPlugin.Settings %}
            {% assign searchValue = headerPlugin.Settings.Search | append: "" %}
            {% if searchValue == "0" %}
                {% assign showSearch = false %}
            {% endif %}
            
            {% assign userValue = headerPlugin.Settings.User | append: "" %}
            {% if userValue == "0" %}
                {% assign showUser = false %}
            {% endif %}
            
            {% assign shoppingCartValue = headerPlugin.Settings.ShoppingCart | append: "" %}
            {% if shoppingCartValue == "0" %}
                {% assign showShoppingCart = false %}
            {% endif %}
            
            {% assign languageSwitchValue = headerPlugin.Settings.LanguageSwitch | append: "" %}
            {% if languageSwitchValue == "0" %}
                {% assign showLanguageSwitch = false %}
            {% endif %}
            
            {% if headerPlugin.Settings.SearchPlaceholder %}
                {% assign searchPlaceholder = headerPlugin.Settings.SearchPlaceholder %}
            {% endif %}
        {% endif %}
        
        <!-- 解析块配置 -->
        {% if headerPlugin.Blocks %}
            {% if headerPlugin.Blocks.Logo %}
                {% assign logoConfig = headerPlugin.Blocks.Logo %}
            {% endif %}
            
            {% if headerPlugin.Blocks.Menu %}
                {% assign menuValue = headerPlugin.Blocks.Menu.Menu | append: "" %}
                {% if menuValue == "0" %}
                    {% assign showMenu = false %}
                {% endif %}
            {% endif %}
        {% endif %}
    {% endif %}
{% endif %}

<!-- 渲染Header -->
{% if showHeader %}
<header class="header {{ headerStyle }}" 
        {% if headerBackgroundColor or headerTextColor %}
        style="{% if headerBackgroundColor %}background-color: {{ headerBackgroundColor }};{% endif %}{% if headerTextColor %}color: {{ headerTextColor }};{% endif %}"
        {% endif %}>
    <div class="container-fluid">
        <div class="row align-items-center">
            
            <!-- Logo区域 -->
            {% if showLogo %}
            <div class="col-lg-3 col-md-4 col-6">
                <div class="logo">
                    <a href="/">
                        {% if logoConfig and logoConfig.Logo %}
                            <img src="{{ logoConfig.Logo }}"
                                 alt="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                                 title="{% if logoConfig.ImageAlt %}{{ logoConfig.ImageAlt }}{% else %}Logo{% endif %}"
                                 class="logo-img"
                                 {% if logoConfig.LogoWidth %}
                                 style="width: {{ logoConfig.LogoWidth }};"
                                 {% endif %}/>
                        {% else %}
                            <!-- 默认Logo -->
                            <img src="/assets/images/logo/default-logo.png" alt="Logo" class="logo-img"/>
                        {% endif %}
                    </a>
                </div>
            </div>
            {% endif %}
            
            <!-- 导航菜单区域 -->
            {% if showMenu %}
            <div class="col-lg-6 col-md-4 d-none d-lg-block">
                <nav class="main-navigation">
                    <!-- 导航菜单通过HTMX加载 -->
                    <div id="desktop-navigation" 
                         hx-get="/home/<USER>" 
                         hx-trigger="load" 
                         hx-select="nav#AccessibleNav">
                        <!-- 导航菜单将通过HTMX加载 -->
                    </div>
                </nav>
            </div>
            {% endif %}
            
            <!-- 功能图标区域 -->
            <div class="col-lg-3 col-md-4 col-6">
                <div class="header-icons d-flex justify-content-end align-items-center">
                    
                    <!-- 搜索图标 -->
                    {% if showSearch %}
                    <div class="search-icon">
                        <i class="icon an an-search" data-toggle="search-drawer"></i>
                    </div>
                    {% endif %}
                    
                    <!-- 用户图标 -->
                    {% if showUser %}
                    <div class="user-icon">
                        <a href="/account/signin">
                            <i class="icon an an-user"></i>
                        </a>
                    </div>
                    {% endif %}
                    
                    <!-- 购物车图标 -->
                    {% if showShoppingCart %}
                    <div class="cart-icon">
                        <a href="/cart">
                            <i class="icon an an-shopping-bag"></i>
                            <span class="cart-count">0</span>
                        </a>
                    </div>
                    {% endif %}
                    
                    <!-- 语言切换 -->
                    {% if showLanguageSwitch %}
                    <div class="language-switch">
                        <!-- 语言切换通过HTMX加载 -->
                        <div hx-get="/home/<USER>" 
                             hx-trigger="load">
                            <!-- 语言切换将通过HTMX加载 -->
                        </div>
                    </div>
                    {% endif %}
                    
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索抽屉 -->
    {% if showSearch %}
    <div class="search-drawer">
        <div class="container">
            <div class="search-form">
                <input type="text" 
                       name="q" 
                       placeholder="{{ searchPlaceholder }}"
                       class="search-input">
                <button type="submit" class="search-button">
                    <i class="icon an an-search"></i>
                </button>
            </div>
        </div>
    </div>
    {% endif %}
    
</header>
{% endif %}

<!-- JavaScript：使用自动注入的数据 -->
<script>
    // 获取自动注入的VisualPageData
    const visualPageData = {{ ViewData.VisualPageData | raw }};
    
    // 在JavaScript中使用Header配置
    if (visualPageData && visualPageData.PluginsByType && visualPageData.PluginsByType.header) {
        const headerConfig = visualPageData.PluginsByType.header[0];
        
        // 例如：动态设置搜索功能
        if (headerConfig && headerConfig.Settings && headerConfig.Settings.Search === "1") {
            // 初始化搜索功能
            console.log('搜索功能已启用');
        }
        
        // 例如：动态设置购物车功能
        if (headerConfig && headerConfig.Settings && headerConfig.Settings.ShoppingCart === "1") {
            // 初始化购物车功能
            console.log('购物车功能已启用');
        }
    }
</script>

{% comment %}
使用说明：
1. 现在Header可以直接使用ViewData.VisualPageData，无需在Controller中传递
2. 支持所有Header相关的可视化配置：显示/隐藏、样式、颜色等
3. 数据自动缓存，性能优异
4. 支持多语言，自动切换
5. 可以在JavaScript中使用相同的数据进行动态交互
{% endcomment %}
