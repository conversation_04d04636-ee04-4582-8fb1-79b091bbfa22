{% comment %}
这是一个示例文件，展示如何在任何页面中使用自动注入的VisualPageData
现在所有页面都可以直接访问这些数据，无需在Controller中手动获取
{% endcomment %}

{% comment %} 
方式1：直接使用ViewData中的JSON字符串
{% endcomment %}
<script>
    // VisualPageData会自动注入到ViewData中
    window.visualPageData = {{ ViewData.VisualPageData | raw }};
    
    console.log('可视化页面数据:', window.visualPageData);
    
    // 现在可以在任何页面的JavaScript中使用这些数据
    if (window.visualPageData && window.visualPageData.PluginsByType) {
        // 例如：获取首页轮播图数据
        const bannerData = window.visualPageData.PluginsByType.banner;
        if (bannerData && bannerData.length > 0) {
            console.log('轮播图数据:', bannerData);
        }
        
        // 例如：获取产品展示配置
        const productDisplay = window.visualPageData.PluginsByType.product_display;
        if (productDisplay && productDisplay.length > 0) {
            console.log('产品展示配置:', productDisplay);
        }
    }
</script>

{% comment %} 
方式2：在Liquid模板中直接使用对象数据
如果需要在模板中使用，可以通过ViewData.VisualPageDataObject访问
{% endcomment %}

{% if ViewData.VisualPageDataObject %}
    {% assign visualData = ViewData.VisualPageDataObject %}
    
    {% comment %} 示例：渲染轮播图 {% endcomment %}
    {% if visualData.PluginsByType.banner %}
        <div class="banner-section">
            {% for banner in visualData.PluginsByType.banner %}
                <div class="banner-item">
                    <h3>{{ banner.Title }}</h3>
                    <p>{{ banner.Description }}</p>
                    {% if banner.ImageUrl %}
                        <img src="{{ banner.ImageUrl }}" alt="{{ banner.Title }}">
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    {% comment %} 示例：渲染产品展示配置 {% endcomment %}
    {% if visualData.PluginsByType.product_display %}
        {% assign productConfig = visualData.PluginsByType.product_display | first %}
        <div class="product-display-config" 
             data-layout="{{ productConfig.Layout }}" 
             data-columns="{{ productConfig.Columns }}">
            <!-- 产品展示区域 -->
        </div>
    {% endif %}
{% endif %}

{% comment %}
使用说明：
1. 现在所有页面都会自动获得VisualPageData，无需在Controller中手动获取
2. 数据会自动缓存30分钟，提高性能
3. 支持多语言，不同语言会有独立的缓存
4. 在JavaScript中使用：window.visualPageData
5. 在Liquid模板中使用：ViewData.VisualPageDataObject
6. 数据结构：{ PluginsByType: { "插件类型": [插件数据数组] } }
{% endcomment %}
