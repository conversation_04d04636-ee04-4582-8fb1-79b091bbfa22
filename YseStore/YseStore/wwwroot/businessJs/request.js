/**
 * 请求模块
 * 封装HTTP请求相关功能
 */

// 默认配置
const defaultConfig = {
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

/**
 * 获取CSRF令牌
 * @returns {string} CSRF令牌值
 */
function getCsrfToken() {
  const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
  return tokenElement ? tokenElement.value : '';
}

/**
 * 格式化URL
 * @param {string} baseURL - 基础URL
 * @param {string} url - 相对URL
 * @returns {string} 完整URL
 */
function formatURL(baseURL, url) {
  if (!baseURL) return url;
  return baseURL.endsWith('/') 
    ? (url.startsWith('/') ? `${baseURL}${url.substring(1)}` : `${baseURL}${url}`)
    : (url.startsWith('/') ? `${baseURL}${url}` : `${baseURL}/${url}`);
}

/**
 * 格式化查询参数
 * @param {object} params - 查询参数对象
 * @returns {string} 查询参数字符串
 */
function formatParams(params) {
  if (!params) return '';
  
  const queryString = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
    
  return queryString ? `?${queryString}` : '';
}

/**
 * 创建请求选项
 * @param {object} options - 请求选项
 * @returns {object} 完整的请求选项
 */
function createRequestOptions(options) {
  const { method, data, headers = {} } = options;
  
  // 默认请求头
  const defaultHeaders = {
    'Accept': 'application/json'
  };
  
  // 对于POST、PUT等请求，添加Content-Type和CSRF令牌
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
    defaultHeaders['Content-Type'] = 'application/json';
    defaultHeaders['RequestVerificationToken'] = getCsrfToken();
  }
  
  // 合并请求头
  const mergedHeaders = { ...defaultHeaders, ...headers };
  
  const requestOptions = {
    method,
    headers: mergedHeaders,
    credentials: 'same-origin'
  };
  
  // 添加请求体（如果有）
  if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
    requestOptions.body = JSON.stringify(data);
  }
  
  return requestOptions;
}

/**
 * 处理响应
 * @param {Response} response - 响应对象
 * @returns {Promise<any>} 处理后的响应数据
 */
async function handleResponse(response) {
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
  }
  
  const contentType = response.headers.get('Content-Type') || '';
  
  if (contentType.includes('application/json')) {
    return response.json();
  } else if (contentType.includes('text/')) {
    return response.text();
  } else {
    return response;
  }
}

/**
 * 请求函数
 * @param {object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function request(options) {
  // 合并配置
  const config = { ...defaultConfig, ...options };
  const { url, method = 'GET', params, data, baseURL } = config;
  
  // 构建完整URL
  let fullURL = formatURL(baseURL, url);
  
  // 添加查询参数到URL (对所有请求类型)
  if (params) {
    fullURL += formatParams(params);
  }
  
  // 创建请求选项
  const requestOptions = createRequestOptions({ method, data, headers: config.headers });
  
  // 记录请求细节 (调试用)
  console.log(`请求: ${method} ${fullURL}`, { 
    数据: data, 
    请求头: requestOptions.headers
  });
  
  // 发送请求
  return fetch(fullURL, requestOptions)
    .then(response => {
      console.log(`响应状态: ${response.status}`);
      return handleResponse(response);
    })
    .then(data => {
      console.log(`响应数据:`, data);
      return data;
    })
    .catch(error => {
      console.error(`请求错误(${method} ${url}):`, error);
      throw error;
    });
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {object} [params] - 查询参数
 * @param {object} [config] - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export function get(url, params, config = {}) {
  return request({
    method: 'GET',
    url,
    params,
    ...config
  });
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {object} [data] - 请求数据
 * @param {object} [config] - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export function post(url, data, config = {}) {
  return request({
    method: 'POST',
    url,
    data,
    ...config
  });
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {object} [data] - 请求数据
 * @param {object} [config] - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export function put(url, data, config = {}) {
  return request({
    method: 'PUT',
    url,
    data,
    ...config
  });
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {object} [params] - 查询参数
 * @param {object} [config] - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export function del(url, params, config = {}) {
  return request({
    method: 'DELETE',
    url,
    params,
    ...config
  });
}

/**
 * 创建一个API实例，设置基础URL
 * @param {string} baseURL - 基础URL
 * @returns {object} API实例
 */
export function createAPI(baseURL) {
  return {
    request: (options) => request({ ...options, baseURL }),
    get: (url, params, config = {}) => get(url, params, { ...config, baseURL }),
    post: (url, data, config = {}) => post(url, data, { ...config, baseURL }),
    put: (url, data, config = {}) => put(url, data, { ...config, baseURL }),
    delete: (url, params, config = {}) => del(url, params, { ...config, baseURL })
  };
}

/**
 * 上传文件
 * @param {string} url - 上传URL
 * @param {FormData} formData - 表单数据
 * @param {object} [config] - 其他配置
 * @returns {Promise<any>} 响应数据
 */
export function uploadFile(url, formData, config = {}) {
  const headers = {
    'RequestVerificationToken': getCsrfToken(),
    ...config.headers
  };
  
  // 上传文件时不设置Content-Type，让浏览器自动设置
  delete headers['Content-Type'];
  
  return fetch(url, {
    method: 'POST',
    headers,
    body: formData,
    credentials: 'same-origin'
  })
  .then(handleResponse)
  .catch(error => {
    console.error(`文件上传错误(${url}):`, error);
    throw error;
  });
}

// 默认导出所有方法
export default {
  request,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  createAPI
}; 